import { AstriaGenerateWithPackPromt } from "@/components/photo-packs/astria-generate-with-pack-prompt";
import { AstriaPromptService } from "@/lib/astria-prompts";
import { PromptData } from "@/types/astria";

interface GenerateWithPackPromptProps {
  params: Promise<{
    packId: string;
    promptId: string;
  }>;
}

export default async function GenerateWithPackPrompt({
  params,
}: GenerateWithPackPromptProps) {
  const { promptId } = await params;
  const astriaPromptService = new AstriaPromptService();

  const promptApiResponse = await astriaPromptService.getPromptById(promptId);

  return (
    <AstriaGenerateWithPackPromt
      promtData={promptApiResponse.data as PromptData}
    />
  );
}
