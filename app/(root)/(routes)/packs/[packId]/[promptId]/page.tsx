import { PromptData } from "@/types/astria";
import { AstriaPromptService } from "@/lib/astria-prompts";
import AstriaPacksPage from "@/components/photo-packs/astria-pack-page";

interface TuneInfo {
  id: number;
  title: string;
}

export interface AstriaPromptData {
  id: number;
  text: string;
  images: string[];
  tune?: TuneInfo;
  tunes?: TuneInfo[];
  created_at: string;
  url: string;
}

interface PackPageProps {
  params: Promise<{
    packId: string;
    promptId: string;
  }>;
}

export default async function PacksPage({ params }: PackPageProps) {
  const { packId, promptId } = await params;

  const astriaPromptService = new AstriaPromptService();
  const promptApiResponse = await astriaPromptService.getPromptById(promptId);

  return (
    <AstriaPacksPage initialPromptData={promptApiResponse.data as PromptData} />
  );
}
