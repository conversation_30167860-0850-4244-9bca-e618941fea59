import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  const tuneId = request.nextUrl.searchParams.get("tuneId");
  if (!tuneId) {
    return NextResponse.json({ error: "Tune ID is required" }, { status: 400 });
  }

  try {
    const response = await fetch(
      `https://api.astria.ai/tunes/${tuneId}/prompts`,
      {
        method: "POST",
        headers: {
          Authorization: `Bearer ${process.env.NEXT_PUBLIC_ASTRIA_API_KEY}`,
        },
        body: await request.formData(),
      }
    );
    if (!response.ok) {
      const errorData = await response.json();
      console.error("Astria API Error:", errorData);
      return NextResponse.json(
        { error: JSON.stringify(errorData) },
        { status: 500 }
      );
    }
    return NextResponse.json({ response: await response.json() });
  } catch (error) {
    console.error("Error in POST request:", error);
    return NextResponse.json(
      { error: "An error occurred while processing your request" },
      { status: 500 }
    );
  }
}
