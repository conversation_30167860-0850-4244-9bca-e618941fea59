"use client"; // Error boundaries must be Client Components

import { useEffect, useState } from "react";
import { Button } from "@/components/ui/Button";
import {
  WifiOff,
  AlertTriangle,
  RefreshCw,
  Home,
  Zap,
  Globe,
  Server,
  Bug,
  Clock
} from "lucide-react";

interface ErrorInfo {
  type: 'network' | 'server' | 'client' | 'timeout' | 'unknown';
  title: string;
  description: string;
  icon: React.ReactNode;
  suggestions: string[];
}

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  const [isOnline, setIsOnline] = useState(true);
  const [isRetrying, setIsRetrying] = useState(false);

  useEffect(() => {
    // Log the error to an error reporting service
    console.error("Application Error:", error);

    // Check network connectivity
    const checkNetworkStatus = () => {
      setIsOnline(navigator.onLine);
    };

    checkNetworkStatus();
    window.addEventListener('online', checkNetworkStatus);
    window.addEventListener('offline', checkNetworkStatus);

    return () => {
      window.removeEventListener('online', checkNetworkStatus);
      window.removeEventListener('offline', checkNetworkStatus);
    };
  }, [error]);

  const getErrorInfo = (): ErrorInfo => {
    const errorMessage = error.message.toLowerCase();

    // Check for network-related errors
    if (!isOnline || errorMessage.includes('network') || errorMessage.includes('fetch')) {
      return {
        type: 'network',
        title: 'Connection Lost',
        description: 'Unable to connect to our servers. Please check your internet connection.',
        icon: <WifiOff className="w-16 h-16 text-blue-400" />,
        suggestions: [
          'Check your internet connection',
          'Try refreshing the page',
          'Disable VPN if active',
          'Contact your network administrator'
        ]
      };
    }

    // Check for server errors (5xx)
    if (errorMessage.includes('500') || errorMessage.includes('502') || errorMessage.includes('503')) {
      return {
        type: 'server',
        title: 'Server Unavailable',
        description: 'Our servers are experiencing issues. We\'re working to fix this.',
        icon: <Server className="w-16 h-16 text-red-400" />,
        suggestions: [
          'Try again in a few minutes',
          'Check our status page',
          'Contact support if issue persists'
        ]
      };
    }

    // Check for timeout errors
    if (errorMessage.includes('timeout') || errorMessage.includes('aborted')) {
      return {
        type: 'timeout',
        title: 'Request Timeout',
        description: 'The request took too long to complete.',
        icon: <Clock className="w-16 h-16 text-yellow-400" />,
        suggestions: [
          'Try again with a stable connection',
          'Reduce request complexity',
          'Contact support if issue persists'
        ]
      };
    }

    // Check for 404 errors
    if (errorMessage.includes('404')) {
      return {
        type: 'client',
        title: 'Content Not Found',
        description: 'The requested content could not be found.',
        icon: <AlertTriangle className="w-16 h-16 text-orange-400" />,
        suggestions: [
          'Check the URL for typos',
          'Go back to the previous page',
          'Visit our homepage',
          'Contact support if you believe this is an error'
        ]
      };
    }

    // Default unknown error
    return {
      type: 'unknown',
      title: 'Unexpected Error',
      description: 'Something unexpected happened. Our team has been notified.',
      icon: <Bug className="w-16 h-16 text-purple-400" />,
      suggestions: [
        'Try refreshing the page',
        'Clear your browser cache',
        'Try again later',
        'Contact support with error details'
      ]
    };
  };

  const handleRetry = async () => {
    setIsRetrying(true);

    // Add a small delay for better UX
    await new Promise(resolve => setTimeout(resolve, 1000));

    try {
      reset();  
    } finally {
      setIsRetrying(false);
    }
  };

  const errorInfo = getErrorInfo();

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900 flex items-center justify-center p-4">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-purple-500/10 rounded-full blur-3xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-cyan-500/5 rounded-full blur-3xl animate-pulse delay-500"></div>
      </div>

      <div className="relative z-10 max-w-2xl w-full">
        {/* Main error card */}
        <div className="bg-gray-900/80 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-8 shadow-2xl">
          {/* Header with icon and status */}
          <div className="text-center mb-8">
            <div className="flex justify-center mb-6">
              <div className="relative">
                {errorInfo.icon}
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full animate-ping"></div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full"></div>
              </div>
            </div>

            <h1 className="text-3xl font-bold text-white mb-3 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
              {errorInfo.title}
            </h1>

            <p className="text-gray-400 text-lg leading-relaxed">
              {errorInfo.description}
            </p>
          </div>

          {/* Error details */}
          <div className="bg-gray-800/50 rounded-xl p-6 mb-8 border border-gray-700/30">
            <h3 className="text-white font-semibold mb-3 flex items-center">
              <Zap className="w-5 h-5 mr-2 text-yellow-400" />
              Error Details
            </h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-400">Type:</span>
                <span className="text-white capitalize">{errorInfo.type}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Status:</span>
                <span className={`${isOnline ? 'text-green-400' : 'text-red-400'}`}>
                  {isOnline ? 'Online' : 'Offline'}
                </span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-400">Time:</span>
                <span className="text-white">{new Date().toLocaleTimeString()}</span>
              </div>
            </div>
          </div>

          {/* Suggestions */}
          <div className="mb-8">
            <h3 className="text-white font-semibold mb-4 flex items-center">
              <Globe className="w-5 h-5 mr-2 text-blue-400" />
              Suggested Actions
            </h3>
            <ul className="space-y-3">
              {errorInfo.suggestions.map((suggestion, index) => (
                <li key={index} className="flex items-start text-gray-300">
                  <div className="w-2 h-2 bg-cyan-400 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                  <span>{suggestion}</span>
                </li>
              ))}
            </ul>
          </div>

          {/* Action buttons */}
          <div className="flex flex-col sm:flex-row gap-4">
            <Button
              onClick={handleRetry}
              disabled={isRetrying}
              className="flex-1 bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white border-0 h-12 text-lg font-semibold transition-all duration-300 transform hover:scale-105"
            >
              {isRetrying ? (
                <>
                  <RefreshCw className="w-5 h-5 mr-2 animate-spin" />
                  Retrying...
                </>
              ) : (
                <>
                  <RefreshCw className="w-5 h-5 mr-2" />
                  Try Again
                </>
              )}
            </Button>

            <Button
              onClick={() => window.location.href = '/'}
              variant="outline"
              className="flex-1 border-gray-600 text-gray-300 hover:bg-gray-800 hover:text-white h-12 text-lg font-semibold transition-all duration-300"
            >
              <Home className="w-5 h-5 mr-2" />
              Go Home
            </Button>
          </div>

          {/* Technical details (collapsible) */}
          <details className="mt-8 group">
            <summary className="cursor-pointer text-gray-400 hover:text-white transition-colors duration-200 font-medium">
              Technical Details
            </summary>
            <div className="mt-4 p-4 bg-gray-800/30 rounded-lg border border-gray-700/20">
              <pre className="text-xs text-gray-400 whitespace-pre-wrap break-words">
                {error.message}
                {error.digest && `\nDigest: ${error.digest}`}
                {error.stack && `\n\nStack Trace:\n${error.stack}`}
              </pre>
            </div>
          </details>
        </div>

        {/* Footer */}
        <div className="text-center mt-6 text-gray-500 text-sm">
          If this problem persists, please contact our support team
        </div>
      </div>
    </div>
  );
}
