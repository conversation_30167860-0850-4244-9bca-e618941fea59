"use client";
import { Images, WifiOff, RefreshCw } from "lucide-react";
import { useState, useCallback } from "react";
import BurstModeLoadingScreen from "../ui/GenerationLoadingScreen";
import { useEventSourceConnection } from "@/hooks/useEventSourceConnection";
import { auth } from "@/lib/firebase";
import { useAuthState } from "react-firebase-hooks/auth";

interface DisplayImageProps {
  loading: boolean;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
}

export default function DisplayImage({
  loading,
  setIsLoading,
}: DisplayImageProps) {
  const [images, setImages] = useState<string[]>([]);
  const [user] = useAuthState(auth);

  // Handle incoming messages
  const handleMessage = useCallback(
    (event: MessageEvent) => {
      try {
        const data = JSON.parse(event.data);

        if (data.type === "new-images") {
          console.log("Received new images:", data);
          setImages((prev) => [...prev, ...data.images]);
          setIsLoading(false);
        }
      } catch (error) {
        console.error("Error parsing message:", error);
      }
    },
    [setIsLoading]
  );

  // Handle connection errors
  const handleError = useCallback((error: Event) => {
    console.error("EventSource connection error:", error);
  }, []);

  // Handle successful connection
  const handleOpen = useCallback(() => {
    console.log("EventSource connection established");
  }, []);

  // Use the EventSource connection hook
  const { status, reconnect } = useEventSourceConnection({
    url: user?.uid ? `/api/socket?userId=${user?.uid}` : "",
    onMessage: handleMessage,
    onError: handleError,
    onOpen: handleOpen,
    maxRetries: 10,
    retryDelay: 1000,
    maxRetryDelay: 30000,
  });

  return (
    <div className="relative h-full">
      {/* Connection Status Indicator */}
      <ConnectionStatus status={status} onReconnect={reconnect} />

      {images.length > 0 ? (
        <div
          className={`grid gap-4 grid-cols-${images.length > 4 ? 3 : images.length}`}
        >
          {images?.map((image, index) => (
            <div
              key={index}
              className="relative aspect-square bg-zinc-700 rounded overflow-hidden"
            >
              <img
                src={image}
                alt={`Generated image ${index + 1}`}
                className="w-full h-full object-cover object-center"
              />
            </div>
          ))}
        </div>
      ) : loading ? (
        <LoadingState />
      ) : (
        <EmptyState />
      )}
    </div>
  );
}

function LoadingState() {
  return (
    <div className="flex items-center justify-center h-full">
      <BurstModeLoadingScreen
        message="Generating Images..."
        className="h-fit py-20 rounded-lg"
      />
    </div>
  );
}

function EmptyState() {
  return (
    <div className="flex items-center justify-center h-full">
      <div className="flex items-center justify-center h-full w-full">
        <div className="text-center text-gray-400">
          <div className="mb-4">
            <Images className="h-10 w-10 m-auto" />
          </div>
          <p>Generated images will appear here</p>
          <p className="text-sm text-gray-500 mt-2">
            Start by describing what you want to create.
          </p>
        </div>
      </div>
    </div>
  );
}

interface ConnectionStatusProps {
  status: {
    isConnected: boolean;
    isReconnecting: boolean;
    retryCount: number;
    lastError?: string;
    isOnline: boolean;
  };
  onReconnect: () => void;
}

function ConnectionStatus({ status, onReconnect }: ConnectionStatusProps) {
  // Don't show anything if connected and online
  if (status.isConnected && status.isOnline) {
    return null;
  }

  // Show offline indicator
  if (!status.isOnline) {
    return (
      <div className="absolute top-2 right-2 z-10 bg-red-500/90 backdrop-blur-sm text-white px-3 py-2 rounded-lg text-sm flex items-center gap-2">
        <WifiOff className="h-4 w-4" />
        <span>Offline</span>
      </div>
    );
  }

  // Show reconnecting indicator
  if (status.isReconnecting) {
    return (
      <div className="absolute top-2 right-2 z-10 bg-yellow-500/90 backdrop-blur-sm text-white px-3 py-2 rounded-lg text-sm flex items-center gap-2">
        <RefreshCw className="h-4 w-4 animate-spin" />
        <span>Reconnecting... ({status.retryCount})</span>
      </div>
    );
  }

  // Show disconnected with manual reconnect option
  if (!status.isConnected) {
    return (
      <div className="absolute top-2 right-2 z-10 bg-red-500/90 backdrop-blur-sm text-white px-3 py-2 rounded-lg text-sm flex items-center gap-2">
        <WifiOff className="h-4 w-4" />
        <span>Disconnected</span>
        <button
          onClick={onReconnect}
          className="ml-2 p-1 hover:bg-white/20 rounded transition-colors"
          title="Reconnect"
        >
          <RefreshCw className="h-3 w-3" />
        </button>
      </div>
    );
  }

  return null;
}
