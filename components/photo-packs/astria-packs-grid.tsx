"use client";
import { cn } from "@/lib/utils";
import type { AstriaPackByIdTypes } from "@/types/astria";
import AstriaPacksCard from "./astria-packs-card";
import { usePacks } from "@/context/PacksContext";
import { useEffect, useMemo } from "react";

interface AstriaPackProps {
  pack: AstriaPackByIdTypes | undefined;
}

export default function AstriaPacksGrid({ pack }: AstriaPackProps) {
  const { setPacksPrompt } = usePacks();

  const combinedPrompts = useMemo(() => {
    return Object.keys(pack?.prompts_per_class || {})
      .map((key) => {
        return pack?.prompts_per_class[
          key as keyof typeof pack.prompts_per_class
        ]?.filter((item) => item.id !== undefined);
      })
      .flat()
      .filter((item): item is NonNullable<typeof item> => Boolean(item));
  }, [pack?.prompts_per_class]);

  useEffect(() => {
    if (combinedPrompts.length > 0) {
      setPacksPrompt(combinedPrompts);
    }
  }, [combinedPrompts, setPacksPrompt]);

  return (
    <div className="min-h-screen  pt-20 px-6">
      <div className="mx-auto">
        <h2
          className={cn(
            "text-3xl md:text-6xl font-black tracking-tight mb-16 text-center bg-gradient-to-r from-cyan-400 via-purple-400 to-pink-400 bg-clip-text text-transparent animate-[gradient-slide_5s_linear_infinite] bg-[length:200%_200%]"
          )}
        >
          {pack?.title.toUpperCase()}
        </h2>
        <div className="p-2 md:py-4 lg:px-20">
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-3 md:gap-4">
            {combinedPrompts.map((imageData, index) => {
              if (!imageData) return null;
              return (
                <AstriaPacksCard
                  imageData={{ ...imageData, index }}
                  packId={pack?.id}
                  key={imageData.id}
                />
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}
