import React, { useEffect, useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import Image from "next/image";
import { cn } from "@/lib/utils";

interface BurstModeLoadingScreenProps {
  message?: string;
  onSkip?: () => void;
  showSkipButton?: boolean;
  skipButtonText?: string;
  className?: string;
}

const BurstModeLoadingScreen: React.FC<BurstModeLoadingScreenProps> = ({
  message = "Processing your request in BurstMode",
  onSkip,
  showSkipButton = false,
  skipButtonText = "Continue in background",
  className,
}) => {
  const [progress, setProgress] = useState(0);
  const [phase, setPhase] = useState(1);
  const [factIndex, setFactIndex] = useState(0);

  const interestingFacts = [
    "Our AI analyzes over 10 million creative elements for each generation",
    "The neural networks powering this use 175 billion parameters",
    "Each pixel is carefully calculated from thousands of training examples",
    "Your unique style signature is being applied to this creation",
    "BurstMode's AI learns from the world's most groundbreaking visual artists",
    "Model training can take 10-15 minutes to complete",
    "Your model will be available for 30 days after creation",
    "Higher quality input images result in better models",
    "Virtual try-on requires both person and garment models",
    "The callback API will save your model automatically when training completes",
  ];

  // Cycle through facts during long waits
  useEffect(() => {
    if (phase >= 2) {
      const factInterval = setInterval(() => {
        setFactIndex((prev) => (prev + 1) % interestingFacts.length);
      }, 5000);
      return () => clearInterval(factInterval);
    }
  }, [phase, interestingFacts.length]);

  // Simulate progress with smoother asymptotic behavior
  useEffect(() => {
    const interval = setInterval(() => {
      setProgress((prev) => {
        // Phase 1: Normal progress up to 75% with smoother increments
        if (phase === 1 && prev < 75) {
          // More frequent but smaller increments for smoother animation
          const increment = Math.max(
            0.2,
            Math.random() * 1.2 * (1 - prev / 100)
          );
          const newValue = prev + increment;

          if (newValue >= 75) {
            setPhase(2);
            return 75;
          }
          return newValue;
        }
        // Phase 2: Slower progress from 75% to 90% with smoother transition
        else if (phase === 2 && prev < 90) {
          const increment = Math.max(0.05, Math.random() * 0.3);
          const newValue = prev + increment;

          if (newValue >= 90) {
            setPhase(3);
            return 90;
          }
          return newValue;
        }
        // Phase 3: Very slow progress approaching but never reaching 100%
        else if (phase === 3) {
          const increment =
            (Math.max(0.02, Math.random() * 0.1) * (98 - prev)) / 10;
          return Math.min(98, prev + increment);
        }

        return prev;
      });
    }, 100); // More frequent updates for smoother animation

    return () => clearInterval(interval);
  }, [phase]);

  // Get a loading message based on the current phase
  const getLoadingMessage = () => {
    if (phase === 1) return message;
    if (phase === 2) return "Creating your visual masterpiece...";
    return "Perfecting every detail...";
  };

  return (
    <motion.div
      className={cn(
        "flex flex-col items-center justify-center w-full h-screen bg-gradient-to-br from-indigo-900 to-purple-900",
        className
      )}
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <div className="relative mb-12">
        {/* Burst animation effect */}
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={`burst-${i}`}
            className="absolute w-4 h-16 bg-gradient-to-t from-pink-500 to-blue-500 rounded-full"
            style={{
              originX: 0.5,
              originY: 0,
              left: "50%",
              top: "50%",
              marginLeft: -8,
              marginTop: -8,
            }}
            animate={{
              rotate: i * 45,
              scaleY: [1, 1.2, 1],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              repeatType: "reverse",
              delay: i * 0.1,
            }}
          />
        ))}

        {/* Central orb with properly masked icon */}
        <motion.div
          className="relative z-10 w-24 h-24  bg-gradient-to-br from-indigo-900 to-purple-900 rounded-full flex items-center justify-center shadow-lg shadow-purple-500/50 overflow-hidden"
          animate={{
            scale: [1, 1.1, 1],
            boxShadow: [
              "0 0 20px 5px rgba(168, 85, 247, 0.4)",
              "0 0 30px 8px rgba(168, 85, 247, 0.6)",
              "0 0 20px 5px rgba(168, 85, 247, 0.4)",
            ],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            repeatType: "reverse",
          }}
        >
          {/* Circular mask for the image */}
          <div className="w-16 h-16 rounded-full overflow-hidden flex items-center justify-center">
            <Image
              src="/app/icon-original.png"
              alt="BurstMode AI"
              width={56}
              height={56}
              className="object-cover"
              style={{ borderRadius: "50%" }}
            />
          </div>
        </motion.div>
      </div>

      {/* Enhanced progress visualization */}
      <div className="mb-8 relative">
        {/* Progress bar with smoother animation */}
        <div className="w-64 h-3 bg-gray-700/60 backdrop-blur-sm rounded-full overflow-hidden mb-4 shadow-inner">
          <motion.div
            className="h-full bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 relative"
            style={{ width: `${Math.min(progress, 100)}%` }}
            transition={{ duration: 0.1 }}
          >
            {/* Shimmering effect on the progress bar */}
            <motion.div
              className="absolute inset-0 w-full h-full bg-gradient-to-r from-transparent via-white to-transparent opacity-30"
              animate={{ x: ["-100%", "100%"] }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
            />
          </motion.div>
        </div>

        {/* Enhanced percentage indicator */}
        <motion.div
          className="absolute -right-10 -top-1 px-2 py-1 bg-white/20 backdrop-blur-sm rounded-md text-xs font-medium text-white"
          animate={{
            scale: progress > 90 ? [1, 1.05, 1] : 1,
          }}
          transition={{
            duration: 0.8,
            repeat: progress > 90 ? Infinity : 0,
            repeatType: "reverse",
          }}
        >
          {Math.round(progress)}%
        </motion.div>
      </div>

      {/* Animated typing dots during later phases */}
      {phase >= 2 && (
        <div className="flex justify-center mb-3">
          <div className="flex space-x-2">
            {[...Array(3)].map((_, i) => (
              <motion.div
                key={`dot-${i}`}
                className="w-2 h-2 bg-white rounded-full"
                animate={{ opacity: [0.4, 1, 0.4], scale: [0.8, 1.2, 0.8] }}
                transition={{
                  duration: 1.2,
                  repeat: Infinity,
                  delay: i * 0.4,
                }}
              />
            ))}
          </div>
        </div>
      )}

      {/* Primary message */}
      <div className="text-center mb-6">
        <motion.p
          className={cn(
            "text-xl md:text-4xl font-bold bg-gradient-to-r from-purple-400 via-blue-500 to-purple-400 bg-clip-text text-transparent animate-[gradient-slide_5s_linear_infinite] bg-[length:200%_200%]"
          )}
          animate={{ opacity: [0.7, 1, 0.7] }}
          transition={{ duration: 2, repeat: Infinity }}
        >
          {getLoadingMessage()}
        </motion.p>
        <br></br>
        <p className="text-sm text-white/80">
          Harnessing the power of AI, one burst at a time
        </p>
      </div>

      {/* Interesting facts section with better transitions */}
      {phase >= 2 && (
        <motion.div
          className="max-w-md bg-white/10 backdrop-blur-sm rounded-lg p-4 mt-6 border border-white/20"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <AnimatePresence mode="wait">
            <motion.p
              key={factIndex}
              className="text-sm text-center text-white"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.5 }}
            >
              <span className="text-pink-300 font-medium">Did you know? </span>
              {interestingFacts[factIndex]}
            </motion.p>
          </AnimatePresence>
        </motion.div>
      )}

      {/* Skip button */}
      {showSkipButton && onSkip && (
        <motion.button
          onClick={onSkip}
          className="mt-8 px-4 py-2 bg-white/10 hover:bg-white/20 backdrop-blur-sm rounded-lg border border-white/20 text-white text-sm font-medium transition-colors duration-200"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1, duration: 0.5 }}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          {skipButtonText}
        </motion.button>
      )}
    </motion.div>
  );
};

export default BurstModeLoadingScreen;
