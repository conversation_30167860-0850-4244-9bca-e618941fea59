"use client";

import { useEffect, useRef, useState, useCallback } from "react";

export interface EventSourceConnectionOptions {
  url: string;
  onMessage?: (event: MessageEvent) => void;
  onError?: (error: Event) => void;
  onOpen?: (event: Event) => void;
  maxRetries?: number;
  retryDelay?: number;
  maxRetryDelay?: number;
  retryMultiplier?: number;
}

export interface ConnectionStatus {
  isConnected: boolean;
  isReconnecting: boolean;
  retryCount: number;
  lastError?: string;
  isOnline: boolean;
}

export function useEventSourceConnection(options: EventSourceConnectionOptions) {
  const {
    url,
    onMessage,
    onError,
    onOpen,
    maxRetries = 10,
    retryDelay = 1000,
    maxRetryDelay = 30000,
    retryMultiplier = 1.5,
  } = options;

  const [status, setStatus] = useState<ConnectionStatus>({
    isConnected: false,
    isReconnecting: false,
    retryCount: 0,
    isOnline: typeof navigator !== "undefined" ? navigator.onLine : true,
  });

  const eventSourceRef = useRef<EventSource | null>(null);
  const retryTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const retryCountRef = useRef(0);
  const isManuallyClosedRef = useRef(false);

  // Calculate retry delay with exponential backoff
  const calculateRetryDelay = useCallback((retryCount: number) => {
    const delay = Math.min(
      retryDelay * Math.pow(retryMultiplier, retryCount),
      maxRetryDelay
    );
    // Add some jitter to prevent thundering herd
    return delay + Math.random() * 1000;
  }, [retryDelay, retryMultiplier, maxRetryDelay]);

  // Clean up existing connection
  const cleanup = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close();
      eventSourceRef.current = null;
    }
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
      retryTimeoutRef.current = null;
    }
  }, []);

  // Connect to EventSource
  const connect = useCallback(() => {
    if (!url || isManuallyClosedRef.current) return;

    cleanup();

    setStatus(prev => ({
      ...prev,
      isReconnecting: retryCountRef.current > 0,
      retryCount: retryCountRef.current,
    }));

    try {
      const eventSource = new EventSource(url);
      eventSourceRef.current = eventSource;

      eventSource.onopen = (event) => {
        console.log("EventSource connected");
        retryCountRef.current = 0;
        setStatus(prev => ({
          ...prev,
          isConnected: true,
          isReconnecting: false,
          retryCount: 0,
          lastError: undefined,
        }));
        onOpen?.(event);
      };

      eventSource.onmessage = (event) => {
        onMessage?.(event);
      };

      eventSource.onerror = (event) => {
        console.error("EventSource error:", event);
        
        setStatus(prev => ({
          ...prev,
          isConnected: false,
          lastError: "Connection error",
        }));

        onError?.(event);

        // Only retry if we haven't exceeded max retries and connection wasn't manually closed
        if (retryCountRef.current < maxRetries && !isManuallyClosedRef.current) {
          const delay = calculateRetryDelay(retryCountRef.current);
          retryCountRef.current++;
          
          console.log(`Retrying connection in ${delay}ms (attempt ${retryCountRef.current}/${maxRetries})`);
          
          retryTimeoutRef.current = setTimeout(() => {
            connect();
          }, delay);
        } else if (retryCountRef.current >= maxRetries) {
          setStatus(prev => ({
            ...prev,
            isReconnecting: false,
            lastError: "Max retries exceeded",
          }));
        }
      };
    } catch (error) {
      console.error("Failed to create EventSource:", error);
      setStatus(prev => ({
        ...prev,
        isConnected: false,
        lastError: "Failed to create connection",
      }));
    }
  }, [url, onMessage, onError, onOpen, maxRetries, calculateRetryDelay, cleanup]);

  // Manual reconnect function
  const reconnect = useCallback(() => {
    retryCountRef.current = 0;
    isManuallyClosedRef.current = false;
    connect();
  }, [connect]);

  // Manual disconnect function
  const disconnect = useCallback(() => {
    isManuallyClosedRef.current = true;
    cleanup();
    setStatus(prev => ({
      ...prev,
      isConnected: false,
      isReconnecting: false,
      retryCount: 0,
    }));
  }, [cleanup]);

  // Handle network status changes
  useEffect(() => {
    const handleOnline = () => {
      console.log("Network came back online, attempting to reconnect...");
      setStatus(prev => ({ ...prev, isOnline: true }));
      
      // Reset retry count when network comes back online
      retryCountRef.current = 0;
      
      // Reconnect after a short delay to ensure network is stable
      setTimeout(() => {
        if (!isManuallyClosedRef.current) {
          connect();
        }
      }, 1000);
    };

    const handleOffline = () => {
      console.log("Network went offline");
      setStatus(prev => ({ 
        ...prev, 
        isOnline: false,
        isConnected: false,
        lastError: "Network offline"
      }));
      cleanup();
    };

    // Set initial online status
    setStatus(prev => ({ ...prev, isOnline: navigator.onLine }));

    window.addEventListener("online", handleOnline);
    window.addEventListener("offline", handleOffline);

    return () => {
      window.removeEventListener("online", handleOnline);
      window.removeEventListener("offline", handleOffline);
    };
  }, [connect, cleanup]);

  // Initial connection
  useEffect(() => {
    if (url) {
      isManuallyClosedRef.current = false;
      connect();
    }

    return () => {
      isManuallyClosedRef.current = true;
      cleanup();
    };
  }, [url, connect, cleanup]);

  return {
    status,
    reconnect,
    disconnect,
  };
}
