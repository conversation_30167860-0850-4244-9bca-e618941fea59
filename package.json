{"name": "photoai-clone", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start -p 8080", "lint": "next lint"}, "dependencies": {"@google/generative-ai": "^0.22.0", "@heroui/select": "^2.4.17", "@hookform/resolvers": "^3.10.0", "@nextui-org/accordion": "^2.2.7", "@nextui-org/system": "^2.4.6", "@nextui-org/theme": "^2.4.5", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-collection": "^1.1.3", "@radix-ui/react-dialog": "^1.1.5", "@radix-ui/react-dropdown-menu": "^2.1.5", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.4", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.2.7", "@react-email/components": "^0.0.39", "@react-email/tailwind": "1.0.5", "@shadcn/ui": "^0.0.4", "@stripe/stripe-js": "^5.6.0", "@supabase/supabase-js": "^2.47.1", "@tabler/icons-react": "^3.29.0", "@types/jszip": "^3.4.0", "@types/nodemailer": "^6.4.17", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "classnames": "^2.5.1", "clsx": "^2.1.1", "dotenv": "^16.4.7", "firebase": "^11.1.0", "firebase-admin": "^13.2.0", "flowbite-react": "^0.10.2", "formidable": "^3.5.2", "framer-motion": "^12.6.4", "googleapis": "^148.0.0", "jszip": "^3.10.1", "lucide-react": "^0.468.0", "motion": "^12.0.6", "next": "15.0.4", "next-themes": "^0.4.4", "nodemailer": "^7.0.3", "react": "^19.0.0", "react-dom": "^19.0.0", "react-firebase-hooks": "^5.1.1", "react-hook-form": "^7.54.2", "react-player": "^2.16.0", "resend": "^4.5.1", "seedrandom": "^3.0.5", "shadcn": "^2.1.6", "shadcn-ui": "^0.9.4", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "styled-components": "^6.1.15", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.24.1"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/formidable": "^3.4.5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.20", "eslint": "9.18.0", "eslint-config-next": "15.1.6", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.16", "ts-jest": "^29.2.5", "typescript": "^5"}, "overrides": {"framer-motion": {"react": "19.0.0", "react-dom": "19.0.0"}}}